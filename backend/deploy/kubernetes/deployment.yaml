apiVersion: apps/v1
kind: Deployment
metadata:
  name: atlas-api
  labels:
    app: atlas-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: atlas-api
  template:
    metadata:
      labels:
        app: atlas-api
    spec:
      containers:
      - name: atlas-api
        image: ghcr.io/atlas-ai/atlas-backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: ENV_MODE
          value: "production"
        - name: REDIS_HOST
          value: "redis-service"
        - name: RABBITMQ_HOST
          value: "rabbitmq-service"
        envFrom:
        - secretRef:
            name: atlas-secrets
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: atlas-worker
  labels:
    app: atlas-worker
spec:
  replicas: 2
  selector:
    matchLabels:
      app: atlas-worker
  template:
    metadata:
      labels:
        app: atlas-worker
    spec:
      containers:
      - name: atlas-worker
        image: ghcr.io/atlas-ai/atlas-backend:latest
        command: ["python", "-m", "dramatiq", "--processes", "4", "--threads", "4", "run_agent_background"]
        env:
        - name: ENV_MODE
          value: "production"
        - name: REDIS_HOST
          value: "redis-service"
        - name: RABBITMQ_HOST
          value: "rabbitmq-service"
        envFrom:
        - secretRef:
            name: atlas-secrets
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
---
apiVersion: v1
kind: Service
metadata:
  name: atlas-api-service
spec:
  selector:
    app: atlas-api
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8000
  type: LoadBalancer
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: atlas-api-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: atlas-api
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
