version: '3.8'

services:
  api:
    image: ghcr.io/atlas-ai/atlas-backend:latest
    ports:
      - "8000:8000"
    environment:
      - ENV_MODE=production
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
    secrets:
      - supabase_service_role_key
      - anthropic_api_key
    networks:
      - atlas-network
    deploy:
      replicas: 5
      update_config:
        parallelism: 2
        delay: 10s
        order: start-first
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 2G
      placement:
        constraints:
          - node.role == worker
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  worker:
    image: ghcr.io/atlas-ai/atlas-backend:latest
    command: python -m dramatiq --processes 8 --threads 8 run_agent_background
    environment:
      - ENV_MODE=production
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
    secrets:
      - supabase_service_role_key
      - anthropic_api_key
    networks:
      - atlas-network
    deploy:
      replicas: 3
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 2G
      placement:
        constraints:
          - node.role == worker

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --bind 0.0.0.0 --protected-mode no --maxmemory 8gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - atlas-network
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          cpus: '1'
          memory: 8G
        reservations:
          cpus: '0.5'
          memory: 4G

  rabbitmq:
    image: rabbitmq:3-management
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - atlas-network
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          cpus: '1'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 2G

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - atlas-network
    deploy:
      replicas: 2
      placement:
        constraints:
          - node.role == manager

networks:
  atlas-network:
    driver: overlay
    attachable: true

volumes:
  redis_data:
  rabbitmq_data:

secrets:
  supabase_service_role_key:
    external: true
  anthropic_api_key:
    external: true
