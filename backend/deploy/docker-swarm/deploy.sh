#!/bin/bash

# Docker Swarm Deployment Script for Atlas Backend
set -e

echo "🚀 Starting Atlas Backend deployment to Docker Swarm..."

# Initialize swarm if not already done
if ! docker info | grep -q "Swarm: active"; then
    echo "🔧 Initializing Docker Swarm..."
    docker swarm init
fi

# Create secrets
echo "🔐 Creating Docker secrets..."
echo "$SUPABASE_SERVICE_ROLE_KEY" | docker secret create supabase_service_role_key - 2>/dev/null || echo "Secret supabase_service_role_key already exists"
echo "$ANTHROPIC_API_KEY" | docker secret create anthropic_api_key - 2>/dev/null || echo "Secret anthropic_api_key already exists"

# Deploy the stack
echo "📦 Deploying Atlas stack..."
docker stack deploy -c docker-compose.swarm.yml atlas

echo "⏳ Waiting for services to start..."
sleep 30

# Check service status
echo "📊 Service status:"
docker service ls

echo "🔍 API service details:"
docker service ps atlas_api

echo "✅ Deployment completed!"
echo "🎉 Atlas Backend is now running on Docker Swarm!"
echo ""
echo "📈 To scale services:"
echo "  docker service scale atlas_api=10"
echo "  docker service scale atlas_worker=5"
echo ""
echo "📊 To monitor:"
echo "  docker service logs -f atlas_api"
echo "  docker service logs -f atlas_worker"
