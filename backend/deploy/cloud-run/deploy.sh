#!/bin/bash

# Google Cloud Run Deployment Script for Atlas Backend
set -e

# Configuration
PROJECT_ID="your-gcp-project-id"
REGION="us-central1"
SERVICE_NAME="atlas-backend"
IMAGE_NAME="gcr.io/$PROJECT_ID/atlas-backend"

echo "🚀 Starting Atlas Backend deployment to Google Cloud Run..."

# Build and push to Google Container Registry
echo "📦 Building and pushing Docker image..."
cd ../..
gcloud builds submit --tag $IMAGE_NAME

# Deploy to Cloud Run
echo "🌐 Deploying to Cloud Run..."
gcloud run deploy $SERVICE_NAME \
    --image $IMAGE_NAME \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --port 8000 \
    --memory 4Gi \
    --cpu 2 \
    --min-instances 1 \
    --max-instances 100 \
    --concurrency 1000 \
    --timeout 1800 \
    --set-env-vars ENV_MODE=production \
    --set-env-vars REDIS_HOST=YOUR_REDIS_IP \
    --set-env-vars RABBITMQ_HOST=YOUR_RABBITMQ_IP \
    --set-secrets SUPABASE_SERVICE_ROLE_KEY=supabase-key:latest \
    --set-secrets ANTHROPIC_API_KEY=anthropic-key:latest

echo "✅ Deployment completed successfully!"

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --platform managed --region $REGION --format 'value(status.url)')
echo "🎉 Atlas Backend is now running at: $SERVICE_URL"
