{"family": "atlas-backend", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "2048", "memory": "4096", "executionRoleArn": "arn:aws:iam::YOUR_ACCOUNT:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::YOUR_ACCOUNT:role/ecsTaskRole", "containerDefinitions": [{"name": "atlas-api", "image": "YOUR_ECR_REPO/atlas-backend:latest", "portMappings": [{"containerPort": 8000, "protocol": "tcp"}], "environment": [{"name": "ENV_MODE", "value": "production"}, {"name": "REDIS_HOST", "value": "YOUR_REDIS_CLUSTER_ENDPOINT"}, {"name": "RABBITMQ_HOST", "value": "YOUR_RABBITMQ_ENDPOINT"}], "secrets": [{"name": "SUPABASE_SERVICE_ROLE_KEY", "valueFrom": "arn:aws:secretsmanager:region:account:secret:atlas/supabase-key"}, {"name": "ANTHROPIC_API_KEY", "valueFrom": "arn:aws:secretsmanager:region:account:secret:atlas/anthropic-key"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/atlas-backend", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:8000/api/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}}]}