#!/bin/bash

# AWS ECS Deployment Script for Atlas Backend
set -e

# Configuration
AWS_REGION="us-east-1"
ECR_REPO="YOUR_ACCOUNT.dkr.ecr.us-east-1.amazonaws.com/atlas-backend"
ECS_CLUSTER="atlas-cluster"
ECS_SERVICE="atlas-api-service"
TASK_DEFINITION="atlas-backend"

echo "🚀 Starting Atlas Backend deployment to AWS ECS..."

# Build and push Docker image
echo "📦 Building Docker image..."
cd ../..
docker build -t atlas-backend:latest .

echo "🏷️ Tagging image for ECR..."
docker tag atlas-backend:latest $ECR_REPO:latest
docker tag atlas-backend:latest $ECR_REPO:$(git rev-parse --short HEAD)

echo "🔐 Logging into ECR..."
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REPO

echo "⬆️ Pushing image to ECR..."
docker push $ECR_REPO:latest
docker push $ECR_REPO:$(git rev-parse --short HEAD)

# Update task definition
echo "📝 Updating ECS task definition..."
cd deploy/aws-ecs
sed "s|YOUR_ECR_REPO|$ECR_REPO|g" task-definition.json > task-definition-updated.json

# Register new task definition
echo "📋 Registering new task definition..."
aws ecs register-task-definition \
    --region $AWS_REGION \
    --cli-input-json file://task-definition-updated.json

# Update service
echo "🔄 Updating ECS service..."
aws ecs update-service \
    --region $AWS_REGION \
    --cluster $ECS_CLUSTER \
    --service $ECS_SERVICE \
    --task-definition $TASK_DEFINITION

# Wait for deployment to complete
echo "⏳ Waiting for deployment to complete..."
aws ecs wait services-stable \
    --region $AWS_REGION \
    --cluster $ECS_CLUSTER \
    --services $ECS_SERVICE

echo "✅ Deployment completed successfully!"

# Clean up
rm task-definition-updated.json

echo "🎉 Atlas Backend is now running on ECS!"
