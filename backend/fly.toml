# fly.toml app configuration file generated for atlas-backend-api on 2025-05-31T17:48:04+05:30
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'atlas-backend-api'
primary_region = 'bom'

[build]

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
